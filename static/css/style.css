/* Global Styles */
body, html {
    height: 100%;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.container-fluid {
    height: 100vh;
    padding: 0;
}

/* Sidebar Styles */
.sidebar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.model-selection {
    background: rgba(248, 249, 250, 0.8);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.form-check {
    margin-bottom: 8px;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.stats-section {
    background: rgba(13, 110, 253, 0.1);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid rgba(13, 110, 253, 0.2);
}

.stat-item {
    margin-bottom: 5px;
}

/* Chat Area Styles */
.chat-area {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.chat-header {
    background: linear-gradient(45deg, #0d6efd, #6610f2) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.messages-container {
    overflow-y: auto;
    background: rgba(248, 249, 250, 0.5);
    min-height: 400px;
}

.welcome-message {
    margin-top: 100px;
}

.welcome-message i {
    color: #6c757d;
    opacity: 0.7;
}

/* Message Styles */
.message-group {
    margin-bottom: 30px;
    animation: fadeInUp 0.5s ease-out;
}

.user-message {
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    color: white;
    padding: 15px 20px;
    border-radius: 20px 20px 5px 20px;
    margin-bottom: 20px;
    max-width: 80%;
    margin-left: auto;
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
}

.responses-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 15px;
    align-items: flex-start;
}

.response-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease, max-height 0.3s ease;
    animation: slideInUp 0.6s ease-out;
    width: 100%;
    max-width: 85%;
    margin: 0;
    position: relative;
}

.response-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.response-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f8f9fa;
    cursor: pointer;
    user-select: none;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.model-badge {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 600;
    margin-right: 10px;
}

.response-content {
    line-height: 1.6;
    color: #333;
    overflow: hidden;
    transition: max-height 0.3s ease, opacity 0.3s ease;
}

/* Collapse/Expand Styles */
.response-card.collapsed .response-content {
    max-height: 0;
    opacity: 0;
    margin-bottom: 0;
}

.response-card.collapsed .response-header {
    margin-bottom: 0;
    border-bottom: none;
}

.toggle-btn {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 1.2em;
    cursor: pointer;
    transition: transform 0.3s ease, color 0.3s ease;
    padding: 5px;
    border-radius: 50%;
}

.toggle-btn:hover {
    color: #0d6efd;
    background: rgba(13, 110, 253, 0.1);
}

.toggle-btn.collapsed {
    transform: rotate(180deg);
}

.response-header:hover {
    background: rgba(248, 249, 250, 0.5);
    border-radius: 8px;
    margin: -5px;
    padding: 15px 5px;
}

/* Markdown Styles */
.response-content h1, .response-content h2, .response-content h3 {
    color: #2c3e50;
    margin-top: 20px;
    margin-bottom: 10px;
}

.response-content code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: #e83e8c;
}

.response-content pre {
    background: #2d3748;
    color: #e2e8f0;
    padding: 15px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 15px 0;
}

.response-content blockquote {
    border-left: 4px solid #0d6efd;
    padding-left: 15px;
    margin: 15px 0;
    color: #6c757d;
    font-style: italic;
}

.response-content ul, .response-content ol {
    padding-left: 20px;
}

/* Loading Animation */
.loading-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    margin: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.spinner-container {
    animation: pulse 2s infinite;
}

.loading-text {
    color: #6c757d;
    font-weight: 500;
}

/* Input Area */
.input-area {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.input-group .form-control {
    border-radius: 15px 0 0 15px;
    border: 2px solid #e9ecef;
    padding: 12px 20px;
    resize: vertical;
    min-height: 50px;
}

.input-group .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.input-group .btn {
    border-radius: 0 15px 15px 0;
    padding: 12px 25px;
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    border: none;
    align-self: flex-end;
}

.input-group .btn:hover {
    background: linear-gradient(45deg, #0b5ed7, #5a0fc8);
    transform: translateY(-1px);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Reset Session Button */
#reset-session-btn {
    background: linear-gradient(45deg, #dc3545, #c82333);
    border: none;
    transition: all 0.3s ease;
}

#reset-session-btn:hover {
    background: linear-gradient(45deg, #c82333, #bd2130);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

#reset-session-btn:active {
    transform: translateY(0);
}

/* Export Button */
#export-btn {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    transition: all 0.3s ease;
}

#export-btn:hover {
    background: linear-gradient(45deg, #20c997, #17a2b8);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

#export-btn:active {
    transform: translateY(0);
}

/* Copy Button */
.copy-btn {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    margin-left: 8px;
}

.copy-btn:hover {
    background: linear-gradient(45deg, #20c997, #17a2b8);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.copy-btn:active {
    transform: translateY(0);
}

.copy-btn.copied {
    background: linear-gradient(45deg, #17a2b8, #6f42c1);
}

.copy-btn i {
    font-size: 10px;
}

/* Improved input area layout */
.input-group {
    align-items: flex-end;
}

.input-group textarea {
    font-family: inherit;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
    .responses-grid {
        grid-template-columns: 1fr;
    }

    .sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80%;
        height: 100%;
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .sidebar.show {
        left: 0;
    }

    .user-message {
        max-width: 95%;
    }

    .response-card {
        max-width: 95%;
    }
}
