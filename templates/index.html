<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mini Chat Foxy</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Sidebar -->
            <div class="col-md-3 bg-light p-3 sidebar">
                <h4 class="mb-4"><i class="fas fa-robot text-primary"></i> Mini Chat Foxy</h4>

                <!-- Model Selection -->
                <div class="mb-4">
                    <label class="form-label fw-bold">Select Models:</label>
                    <div class="model-selection">
                        {% for model in models %}
                        <div class="form-check">
                            <input class="form-check-input model-checkbox" type="checkbox" 
                                   value="{{ model.id }}" id="model-{{ model.id }}" checked>
                            <label class="form-check-label" for="model-{{ model.id }}">
                                {{ model.name }}
                            </label>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Character Selection -->
                <div class="mb-4">
                    <label for="character-select" class="form-label fw-bold">Select Character:</label>
                    <select class="form-select" id="character-select">
                        {% for character in characters %}
                        <option value="{{ character.id }}">{{ character.name }}</option>
                        {% endfor %}
                    </select>
                    <small class="text-muted mt-1 d-block" id="character-description">
                        {{ characters[0].personality }}
                    </small>
                </div>

                <!-- Prompt Version Selection -->
                <div class="mb-4">
                    <label for="prompt-version-select" class="form-label fw-bold">Select Prompt Version:</label>
                    <select class="form-select" id="prompt-version-select">
                        {% for version in prompt_versions %}
                        <option value="{{ version.id }}">{{ version.name }}</option>
                        {% endfor %}
                    </select>
                    <small class="text-muted mt-1 d-block" id="version-description">
                        {{ prompt_versions[0].description }}
                    </small>
                </div>

                <!-- Response Controls -->
                <div class="mb-4">
                    <h6 class="fw-bold">Controls:</h6>
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-secondary btn-sm" onclick="toggleAllResponses(true)">
                            <i class="fas fa-compress-alt me-1"></i> Collapse All
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="toggleAllResponses(false)">
                            <i class="fas fa-expand-alt me-1"></i> Expand All
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="resetSession()" id="reset-session-btn">
                            <i class="fas fa-refresh me-1"></i> Reset Session
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="exportChatHistory()" id="export-btn">
                            <i class="fas fa-download me-1"></i> Export CSV
                        </button>
                    </div>
                </div>

                <!-- Stats -->
                <div class="stats-section">
                    <h6 class="fw-bold">Statistics:</h6>
                    <div class="stat-item">
                        <small>Session ID: <span id="session-id" class="text-muted">{{ session_id[:8] }}...</span></small>
                    </div>
                    <div class="stat-item">
                        <small>Selected Models: <span id="selected-count">{{ models|length }}</span></small>
                    </div>
                    <div class="stat-item">
                        <small>Messages Sent: <span id="message-count">{{ chat_history|length }}</span></small>
                    </div>
                </div>
            </div>

            <!-- Main Chat Area -->
            <div class="col-md-9 d-flex flex-column chat-area">
                <!-- Chat Header -->
                <div class="chat-header bg-primary text-white p-3">
                    <h5 class="mb-0">
                        <i class="fas fa-comments"></i>
                        Chat with <span id="current-character">{{ characters[0].name }}</span>
                    </h5>
                </div>

                <!-- Messages Area -->
                <div class="flex-grow-1 p-3 messages-container" id="messages-container">
                    {% if chat_history %}
                        <!-- Load existing chat history -->
                        <div id="chat-history-container">
                            <!-- Chat history will be loaded by JavaScript -->
                        </div>
                    {% else %}
                        <div class="welcome-message text-center text-muted">
                            <i class="fas fa-robot fa-3x mb-3"></i>
                            <h4>Welcome to Mini Chat Foxy!</h4>
                            <p>Select models and character, then send a message to start chatting.</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Loading Animation -->
                <div class="loading-container text-center p-4" id="loading-container" style="display: none;">
                    <div class="spinner-container">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="loading-text mt-2">
                            <i class="fas fa-magic"></i> Generating responses from <span id="loading-models">5</span> models...
                        </div>
                    </div>
                </div>

                <!-- Input Area -->
                <div class="input-area p-3 border-top">
                    <div class="input-group">
                        <textarea class="form-control" id="message-input"
                               placeholder="Type your message here..." maxlength="1000" rows="3"></textarea>
                        <button class="btn btn-primary" type="button" id="send-button">
                            <i class="fas fa-paper-plane"></i> Send
                        </button>
                    </div>
                    <small class="text-muted">Press Enter to send message (Shift+Enter for new line)</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Markdown-it for rendering -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <!-- Session data -->
    <script>
        window.SESSION_ID = '{{ session_id }}';
        window.CHAT_HISTORY = {{ chat_history|tojson }};
    </script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
